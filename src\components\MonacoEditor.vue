<script setup lang="ts">
import * as monaco from 'monaco-editor'
import { loader } from '@monaco-editor/loader'
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

interface Props {
  modelValue: string
  language?: string
  theme?: string
  height?: string
  width?: string
  readonly?: boolean
  options?: monaco.editor.IStandaloneEditorConstructionOptions
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'ready', editor: monaco.editor.IStandaloneCodeEditor): void
}

const props = withDefaults(defineProps<Props>(), {
  language: 'json',
  theme: 'vs-dark',
  height: '400px',
  width: '100%',
  readonly: false,
  options: () => ({})
})

const emit = defineEmits<Emits>()

const editorContainer = ref<HTMLDivElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
  automaticLayout: true,
  formatOnPaste: true,
  formatOnType: true,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  wordWrap: 'on',
  tabSize: 2,
  insertSpaces: true,
  detectIndentation: false,
  folding: true,
  lineNumbers: 'on',
  glyphMargin: false,
  lineDecorationsWidth: 0,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'line',
  contextmenu: true,
  mouseWheelZoom: true,
  fontSize: 14,
  fontFamily: 'Consolas, "Courier New", monospace',
}

async function initEditor() {
  if (!editorContainer.value) return

  try {
    // 配置Monaco Editor
    loader.config({
      paths: {
        vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
      }
    })

    const monacoInstance = await loader.init()
    
    // 创建编辑器实例
    editor = monacoInstance.editor.create(editorContainer.value, {
      value: props.modelValue,
      language: props.language,
      theme: props.theme,
      readOnly: props.readonly,
      ...defaultOptions,
      ...props.options,
    })

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      if (editor) {
        const value = editor.getValue()
        emit('update:modelValue', value)
        emit('change', value)
      }
    })

    // 设置JSON语言特性
    if (props.language === 'json') {
      monacoInstance.languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: true,
        allowComments: false,
        schemas: [],
        enableSchemaRequest: false,
      })
    }

    emit('ready', editor)
  } catch (error) {
    console.error('Monaco Editor initialization failed:', error)
  }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    editor.setValue(newValue)
  }
})

// 监听语言变化
watch(() => props.language, (newLanguage) => {
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage)
    }
  }
})

// 监听主题变化
watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme)
  }
})

// 格式化JSON
function formatJson() {
  if (editor && props.language === 'json') {
    editor.getAction('editor.action.formatDocument')?.run()
  }
}

// 验证JSON
function validateJson(): { isValid: boolean; error?: string } {
  if (!editor || props.language !== 'json') {
    return { isValid: true }
  }

  try {
    const value = editor.getValue()
    if (value.trim()) {
      JSON.parse(value)
    }
    return { isValid: true }
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Invalid JSON' 
    }
  }
}

// 获取编辑器实例
function getEditor() {
  return editor
}

// 设置编辑器值
function setValue(value: string) {
  if (editor) {
    editor.setValue(value)
  }
}

// 获取编辑器值
function getValue() {
  return editor ? editor.getValue() : ''
}

// 暴露方法给父组件
defineExpose({
  formatJson,
  validateJson,
  getEditor,
  setValue,
  getValue,
})

onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})
</script>

<template>
  <div 
    ref="editorContainer" 
    :style="{ height: props.height, width: props.width }"
    class="monaco-editor-container"
  />
</template>

<style scoped>
.monaco-editor-container {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}
</style>
